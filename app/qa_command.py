"""
QA Command Handler Module

This module handles the /qa Slack command for creating Jira QA tickets.
"""

import logging
import traceback
from typing import Op<PERSON>, <PERSON><PERSON>
import psycopg

from .utils import get_robot_by_channel
from .jira_client import create_qa_ticket
from .info_command import validate_robot_serial

logger = logging.getLogger(__name__)


def handle_qa_command_logic(
    portal_conn: psycopg.Connection,
    channel_name: str,
    user_name: str,
    description: str,
    robot_serial: Optional[str] = None,
) -> Tuple[bool, str, Optional[str]]:
    """
    Handle the business logic for the /qa command.
    
    Args:
        portal_conn: Database connection to portal
        channel_name: Slack channel name
        user_name: Slack username who invoked the command
        description: QA issue description from the command text
        robot_serial: Optional robot serial if specified in command
        
    Returns:
        tuple: (success: bool, message: str, private_message: Optional[str])
    """
    try:
        # Validate that description is provided
        if not description or description.strip() == "":
            error_msg = "❌ Error: Please provide a description for the QA ticket: `/qa <description>`"
            return False, error_msg, None

        # Determine robot serial
        detected_robot = None
        if robot_serial:
            # Robot was specified as argument - validate it
            if not validate_robot_serial(robot_serial):
                error_msg = f"❌ Error: Invalid robot serial format '{robot_serial}'. Expected format: slayer# or reaper#"
                return False, error_msg, None
            detected_robot = robot_serial
            logger.info(f"User @{user_name} created QA ticket for specified robot {robot_serial} in channel {channel_name}")
        else:
            # No robot specified, try to determine from channel
            robot_from_channel = get_robot_by_channel(portal_conn, channel_name)
            if robot_from_channel:
                detected_robot = robot_from_channel
                logger.info(f"User @{user_name} created QA ticket for channel robot {detected_robot} in channel {channel_name}")
            else:
                # No robot found from channel - that's okay, we'll create the ticket without robot info
                logger.info(f"User @{user_name} created QA ticket with no robot association in channel {channel_name}")

        # Create the Jira ticket
        ticket_url = create_qa_ticket(
            description=description.strip(),
            robot_serial=detected_robot,
            user_name=user_name
        )
        
        if not ticket_url:
            error_msg = "❌ Error: Failed to create QA ticket. Please try again or contact support."
            return False, error_msg, None

        # Format the success message
        success_msg = f"✅ QA ticket created successfully!\n🎫 {ticket_url}"
        if detected_robot:
            success_msg += f"\n🤖 Robot: {detected_robot}"
        success_msg += f"\n👤 Created by: @{user_name}"
        
        return True, success_msg, None

    except Exception as e:
        logger.error(f"Error handling /qa command: {e}")
        error_msg = "❌ Error: Failed to process QA ticket request"
        traceback.print_exc()
        return False, error_msg, None


def parse_qa_command_args(command_text: str) -> Tuple[str, Optional[str]]:
    """
    Parse the /qa command arguments to extract description and optional robot serial.
    
    Args:
        command_text: The text following the /qa command
        
    Returns:
        tuple: (description: str, robot_serial: Optional[str])
    """
    if not command_text:
        return "", None
    
    # Split the command text into words
    parts = command_text.strip().split()
    
    if not parts:
        return "", None
    
    # Check if the first part looks like a robot serial
    first_part = parts[0].lower()
    if (first_part.startswith("slayer") or first_part.startswith("reaper")) and len(parts) > 1:
        # First part is robot serial, rest is description
        robot_serial = parts[0]
        description = " ".join(parts[1:])
        return description, robot_serial
    else:
        # All parts are description
        description = " ".join(parts)
        return description, None


def handle_qa_command(
    portal_conn: psycopg.Connection,
    channel_name: str,
    user_name: str,
    command_text: str,
) -> Tuple[bool, str]:
    """
    Handle the /qa command from Slack.
    
    Args:
        portal_conn: Database connection to portal
        channel_name: Slack channel name
        user_name: Slack username who invoked the command
        command_text: The text following the /qa command
        
    Returns:
        tuple: (success: bool, response_message: str)
    """
    try:
        # Parse the command arguments
        description, robot_serial = parse_qa_command_args(command_text)
        
        # Handle the command logic
        success, message, _ = handle_qa_command_logic(
            portal_conn, channel_name, user_name, description, robot_serial
        )
        
        return success, message
        
    except Exception as e:
        logger.error(f"Error in handle_qa_command: {e}")
        return False, "❌ Error: Failed to process QA command"
