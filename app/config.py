import os

# Database connection strings
ALEXBOT_DB_URL = os.getenv("ALEXBOT_DB_URL", "postgres://your_username:your_password@localhost:5432/alexbotdb")
PORTAL_DB_URL = os.getenv("PORTAL_DB_URL", "postgres://your_username:your_password@localhost:5432/portal")

# Slack configuration
SLACK_TOKEN = os.getenv("SLACK_TOKEN", "xoxb-your-slack-token-here")
SLACK_APP_TOKEN = os.getenv("SLACK_APP_TOKEN", "xapp-your-app-token-here")
SUPPORT_CHANNEL_OVERRIDE = os.getenv("SUPPORT_CHANNEL_OVERRIDE")

# Prometheus configuration
PROMETHEUS_URL = os.getenv("PROMETHEUS_URL", "http://localhost:9090")

# ROSY configuration
ROSY_URL = os.getenv("ROSY_URL", "http://localhost:8080")

# Jira configuration
JIRA_BASE_URL = os.getenv("JIRA_BASE_URL", "https://carbonrobotics-dev.atlassian.net/")
JIRA_USERNAME = "<EMAIL>" # os.getenv("ALEXBOT_JIRA_USER")
JIRA_TOKEN = os.getenv("ALEXBOT_JIRA_API_TOKEN")

# Monitoring configuration
DISTANCE_THRESHOLD_METERS = float(os.getenv("DISTANCE_THRESHOLD_METERS", "1000"))
MONITORING_INTERVAL_SECONDS = int(os.getenv("MONITORING_INTERVAL_SECONDS", "60"))

# Speed monitoring configuration
SPEED_DIFFERENCE_THRESHOLD_MPH = float(os.getenv("SPEED_DIFFERENCE_THRESHOLD_MPH", "0.5"))
SPEED_MONITORING_WINDOW_MINUTES = int(os.getenv("SPEED_MONITORING_WINDOW_MINUTES", "5"))
SPEED_ALERT_INTERVAL_MINUTES = int(os.getenv("SPEED_ALERT_INTERVAL_MINUTES", "30"))
SPEED_CONSTANCY_TOLERANCE_MPH = float(os.getenv("SPEED_CONSTANCY_TOLERANCE_MPH", "0.01"))
SPEED_CONSTANCY_WINDOW_MINUTES = int(os.getenv("SPEED_CONSTANCY_WINDOW_MINUTES", "15"))

# Thinning box monitoring configuration
MAX_THINNING_BOX_THRESHOLD_INCHES = float(os.getenv("MAX_THINNING_BOX_THRESHOLD_INCHES", "7.0"))
THINNING_BOX_ALERT_INTERVAL_MINUTES = int(os.getenv("THINNING_BOX_ALERT_INTERVAL_MINUTES", "30"))

# Visualization configuration
VISUALIZATION_RATE_LIMIT_MINUTES = int(os.getenv("VISUALIZATION_RATE_LIMIT_MINUTES", "15"))
VISUALIZATION_WEEDING_REQUIREMENT_MINUTES = int(os.getenv("VISUALIZATION_WEEDING_REQUIREMENT_MINUTES", "3"))

# Support summary configuration
SUPPORT_SUMMARY_MAX_ISSUES = int(os.getenv("SUPPORT_SUMMARY_MAX_ISSUES", "100"))

# Logging configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
