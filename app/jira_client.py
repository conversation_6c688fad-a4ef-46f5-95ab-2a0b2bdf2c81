import json
import logging
import requests
import traceback
from datetime import datetime
from typing import Any, Optional
from .config import JIRA_BASE_URL, JIRA_USERNAME, JIRA_TOKEN

logger = logging.getLogger(__name__)



class JiraClient:
    def __init__(self):
        self.base_url = JIRA_BASE_URL.rstrip('/') if JIRA_BASE_URL else None
        self.username = JIRA_USERNAME
        self.token = JIRA_TOKEN
        
        assert JIRA_BASE_URL is not None, "JIRA_BASE_URL is not set"
        assert JIRA_USERNAME is not None, "JIRA_USERNAME is not set"
        assert JIRA_TOKEN is not None, "JIRA_TOKEN is not set"

        self.session = requests.Session()
        self.session.auth = (self.username, self.token)
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

    def create_issue(self, project_key: str, issue_type: str, summary: str, 
                    description: str, custom_fields: Optional[dict[str, Any]] = None) -> Optional[dict[str, Any]]:
        try:
            url = f"{self.base_url}/rest/api/3/issue"
            
            # Build the issue data
            issue_data = {
                "fields": {
                    "project": {"key": project_key},
                    "issuetype": {"name": issue_type},
                    "summary": summary
                }
            }
            
            if description:
                issue_data["fields"]["description"] = {
                    "type": "doc",
                    "version": 1,
                    "content": [
                        {
                            "type": "paragraph",
                            "content": [
                                {
                                    "type": "text",
                                    "text": description
                                }
                            ]
                        }
                    ]
                }
            
            # Add custom fields if provided
            if custom_fields:
                issue_data["fields"].update(custom_fields)
            
            logger.info(f"Creating Jira issue in project {project_key}: {summary}")
            
            response = self.session.post(url, data=json.dumps(issue_data))
            
            if response.status_code == 201:
                issue = response.json()
                logger.info(f"Successfully created Jira issue: {issue['key']}")
                return issue
            else:
                logger.error(f"Failed to create Jira issue. Status: {response.status_code}, Response: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating Jira issue: {e}")
            traceback.print_exc()
            return None

    def add_comment(self, issue_key: str, comment_text: str) -> Optional[dict[str, Any]]:
        try:
            url = f"{self.base_url}/rest/api/3/issue/{issue_key}/comment"
            
            comment_data = {
                "body": {
                    "type": "doc",
                    "version": 1,
                    "content": [
                        {
                            "type": "paragraph",
                            "content": [
                                {
                                    "type": "text",
                                    "text": comment_text
                                }
                            ]
                        }
                    ]
                }
            }
            
            logger.info(f"Adding comment to Jira issue {issue_key}")
            
            response = self.session.post(url, data=json.dumps(comment_data))
            
            if response.status_code == 201:
                comment = response.json()
                logger.info(f"Successfully added comment to issue {issue_key}")
                return comment
            else:
                logger.error(
                    f"Failed to add comment to issue {issue_key}. " \
                        f"Status: {response.status_code}, Response: {response.text}"
                    )
                return None
                
        except Exception as e:
            logger.error(f"Error adding comment to issue {issue_key}: {e}")
            traceback.print_exc()
            return None

    def get_issue_url(self, issue_key: str) -> str:
        return f"{self.base_url}/browse/{issue_key}"

    def search_issues(self, jql: str, max_results: int = 50) -> Optional[list[dict[str, Any]]]:

        try:
            url = f"{self.base_url}/rest/api/3/search"

            params = {
                "jql": jql,
                "maxResults": max_results
            }

            logger.info(f"Searching Jira issues with JQL: {jql}")

            response = self.session.get(url, params=params)

            if response.status_code == 200:
                data = response.json()
                issues = data.get("issues", [])
                logger.info(f"Found {len(issues)} issues")
                return issues
            else:
                logger.error(f"Failed to search Jira issues. Status: {response.status_code}, Response: {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error searching Jira issues: {e}")
            traceback.print_exc()
            return None

    def get_issues_modified_since(self, since_datetime: datetime, projects: Optional[list[str]] = None) -> Optional[list[dict[str, Any]]]:
        try:
            # Format datetime for JQL (YYYY-MM-DD HH:mm)
            since_str = since_datetime.strftime("%Y-%m-%d %H:%M")

            # Build JQL query
            jql_parts = [f'created >= "{since_str}"']

            if projects:
                project_filter = " OR ".join([f'project = "{p}"' for p in projects])
                jql_parts.append(f"({project_filter})")

            jql = " AND ".join(jql_parts)
            jql += " ORDER BY created DESC"
            
            logger.info(f"JQL: {jql}")

            return self.search_issues(jql, max_results=100)

        except Exception as e:
            logger.error(f"Error getting issues modified since {since_datetime}: {e}")
            traceback.print_exc()
            return None


def create_qa_ticket(description: str, robot_serial: Optional[str] = None, user_name: Optional[str] = None) -> Optional[str]:
    try:
        client = JiraClient()
        
        # Build the summary
        summary = "QA Issue"
        if robot_serial:
            summary += f" - {robot_serial}"
        
        # Build the full description with context
        full_description = description
        if robot_serial or user_name:
            full_description += "\n\n--- Additional Information ---"
            if robot_serial:
                full_description += f"\nRobot: {robot_serial}"
            if user_name:
                full_description += f"\nReported by: @{user_name}"
        
        # Create the issue (using 'QA' project and 'Task' issue type as defaults)
        # These can be made configurable via environment variables if needed
        issue = client.create_issue(
            project_key="SOFTWARE",  # This should be configured based on your Jira setup
            issue_type="Bug",  # This can also be made configurable
            summary=summary,
            description=full_description
        )
        
        if issue:
            return client.get_issue_url(issue['key'])
        else:
            return None
            
    except Exception as e:
        logger.error(f"Error creating QA ticket: {e}")
        traceback.print_exc()
        return None
